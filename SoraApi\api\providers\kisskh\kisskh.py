import asyncio
import re
import base64
import struct
from dataclasses import dataclass, fields
from typing import Callable, Awaitable, List, Optional, Dict

import aiohttp
from urllib.parse import urljoin, quote

from ... import utils
from ..base_provider import BaseProvider, SubtitleCallback, ExtractorCallback

# --- No decryption logic needed for this version ---

# --- Dataclasses ---
@dataclass
class KisskhMedia: id: int; title: str; thumbnail: Optional[str] = None; episodesCount: Optional[int] = None
@dataclass
class KisskhEpisode: id: int; number: int
@dataclass
class KisskhMediaDetail: id: int; title: str; episodes: List[KisskhEpisode]; thumbnail: Optional[str] = None; description: Optional[str] = None; releaseDate: Optional[str] = None; type: Optional[str] = None

# --- Provider Class ---
class KisskhProvider(BaseProvider):
    def __init__(self, session: aiohttp.ClientSession):
        super().__init__(session)
        self.name = "Kisskh"
        self.main_url = "https://kisskh.ovh" # Corrected base URL
        self.api_headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Accept": "application/json, text/plain, */*",
        }
        
    async def _api_call(self, url: str, **kwargs) -> Optional[Dict | List]:
        headers = {**self.api_headers, **kwargs.pop('headers', {})}
        try:
            kwargs.setdefault('timeout', 20)
            async with self.session.get(url, ssl=False, headers=headers, **kwargs) as response:
                response.raise_for_status()
                return await response.json(content_type=None)
        except aiohttp.ClientResponseError as e:
            print(f"[Kisskh-Error] HTTP Error for {url}: {e.status}, {e.message}")
        except Exception as e:
            print(f"[Kisskh-Error] API call exception for {url}: {e}")
        return None

    def _get_sanitized_title(self, title: str) -> str:
        return re.sub(r'[^a-zA-Z0-9]', '-', title)

    async def _search(self, query: str) -> Optional[List[KisskhMedia]]:
        search_url = f"{self.main_url}/api/DramaList/Search?q={quote(query)}&type=0"
        data = await self._api_call(search_url, headers={'Referer': self.main_url})
        if not (data and isinstance(data, list)): return None
        results, valid_fields = [], {f.name for f in fields(KisskhMedia)}
        for item in data:
            if isinstance(item, dict) and 'id' in item and 'title' in item:
                results.append(KisskhMedia(**{k: v for k, v in item.items() if k in valid_fields}))
        return results

    async def _get_media_details(self, media_id: int) -> Optional[KisskhMediaDetail]:
        detail_url = f"{self.main_url}/api/DramaList/Drama/{media_id}?isq=false"
        data = await self._api_call(detail_url)
        if data and isinstance(data, dict):
            try:
                ep_fields = {f.name for f in fields(KisskhEpisode)}
                episodes = [KisskhEpisode(**{k: v for k, v in ep.items() if k in ep_fields})
                            for ep in data.get('episodes', []) if isinstance(ep, dict)]
                data['episodes'] = episodes
                detail_fields = {f.name for f in fields(KisskhMediaDetail)}
                return KisskhMediaDetail(**{k: v for k, v in data.items() if k in detail_fields})
            except Exception as e:
                print(f"[Kisskh-Error] Dataclass creation failed: {e}")
        return None

    async def _handle_m3u8(self, url: str, callback: ExtractorCallback):
        try:
            async with self.session.get(url, headers={'Referer': self.main_url}) as response:
                content = await response.text()
            
            for line in content.splitlines():
                if line.startswith("https://") and ".m3u8" in line:
                    res_match = re.search(r'/(\d+p)\.m3u8', line)
                    quality = int(res_match.group(1).replace('p','')) if res_match else 720
                    await callback(utils.ExtractorLink(
                        f"Kisskh ({quality}p)", line, self.main_url, quality, is_m3u8=True
                    ))
        except Exception as e:
            print(f"[Kisskh-M3U8-Error] {e}")

    async def get_streams(self, details: Dict, season: Optional[int], episode: Optional[int],
                            subtitle_callback: SubtitleCallback, callback: ExtractorCallback):
        print("[PROVIDER] Trying Kisskh...")
        title = details.get("title") or details.get("name")
        if not title: return

        search_results = await self._search(title)
        if not search_results: return

        media_id = search_results[0].id
        media_details = await self._get_media_details(media_id)
        if not media_details or not media_details.episodes: return

        kisskh_title = media_details.title
        target_episode = next((ep for ep in media_details.episodes if ep.number == episode), 
                              media_details.episodes[0] if episode == 1 and media_details.episodes else None)
        if not target_episode: return
            
        episode_id = target_episode.id
        episode_number = target_episode.number
        
        try:
            # --- Build the correct Referer URL (this remains critical) ---
            sanitized_title = self._get_sanitized_title(kisskh_title)
            referer_url = (f"{self.main_url}/Drama/{sanitized_title}/"
                           f"Episode-{episode_number}?id={media_id}&ep={episode_id}&page=0&pageSize=100")
            api_headers = {**self.api_headers, 'Referer': referer_url}
            
            print(f"[Kisskh] Using Referer: {referer_url}")
            
            # --- Fetch video sources directly ---
            source_url = f"{self.main_url}/api/DramaList/Episode/{episode_id}.png?err=false&ts=&time="
            sources_data = await self._api_call(source_url, headers=api_headers)
            if isinstance(sources_data, dict):
                video_url = sources_data.get('Video')
                if video_url and ".m3u8" in video_url:
                    await self._handle_m3u8(video_url, callback)

            # --- Fetch subtitles directly ---
            sub_url = f"{self.main_url}/api/Sub/{episode_id}"
            sub_list = await self._api_call(sub_url, headers=api_headers)
            if isinstance(sub_list, list):
                for sub in sub_list:
                    if isinstance(sub, dict):
                        src, lang = sub.get('src'), sub.get('label')
                        if src and lang:
                            # Note: No subtitle decryption seems necessary in this version.
                            await subtitle_callback(utils.SubtitleFile(lang=lang, url=src))

        except Exception as e:
            print(f"[Kisskh-Error] An exception occurred during stream fetching: {e}")