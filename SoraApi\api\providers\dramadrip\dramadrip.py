import aiohttp
import asyncio
import re
import json
from bs4 import BeautifulSoup
from typing import Callable, Awaitable, List, Optional, Dict
from urllib.parse import urlparse, urljoin, quote
from ... import utils
from ..base_provider import BaseProvider, SubtitleCallback, ExtractorCallback

class DramaDripProvider(BaseProvider):
    def __init__(self, session: aiohttp.ClientSession):
        super().__init__(session)
        self.name = "DramaDrip"
        self.main_url = "https://dramadrip.com"
        self.driveseed_url = "https://driveseed.org"
        self.domains_url = "https://raw.githubusercontent.com/phisher98/TVVVV/refs/heads/main/domains.json"
        self.cached_domains = None

    async def _get_soup(self, url: str, headers: Dict[str, str] = None) -> BeautifulSoup:
        if headers is None:
            headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'}
        try:
            async with self.session.get(url, headers=headers, ssl=False, timeout=20) as response:
                response.raise_for_status()
                return BeautifulSoup(await response.text(), "html.parser")
        except Exception as e:
            print(f"[DramaDrip-Error] Failed to get soup from {url} due to: {e}")
            return BeautifulSoup("", "html.parser")
            
    async def _get_domains(self) -> Dict:
        if self.cached_domains is None:
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(self.domains_url, timeout=10) as response:
                        response.raise_for_status()
                        self.cached_domains = await response.json(content_type=None)
                        print("[DramaDrip-Debug] Successfully fetched and cached domains.")
            except Exception as e:
                print(f"[DramaDrip-Error] Failed to fetch domains: {e}. Falling back to default URL.")
                self.cached_domains = {"dramadrip": self.main_url}
        return self.cached_domains

    async def _search(self, query: str) -> Optional[str]:
        domains = await self._get_domains()
        base_url = domains.get("dramadrip", self.main_url)
        search_url = f"{base_url}/?s={quote(query)}"
        print(f"[DramaDrip-Debug] Searching with URL: {search_url}")
        
        document = await self._get_soup(search_url)
        first_result = document.select_one("article h2.entry-title > a")
        if first_result and first_result.get("href"):
            found_url = first_result["href"]
            print(f"[DramaDrip-Debug] Found search result: {found_url}")
            return found_url
        
        print(f"[DramaDrip-Debug] No search result found for query: '{query}'")
        return None

    # --- Driveseed Extractor ---
    async def _driveseed_instant_link(self, final_link: str) -> Optional[str]:
        try:
            print(f"[Driveseed-Extractor] Processing Instant Link: {final_link}")
            parsed_uri = urlparse(final_link)
            host = parsed_uri.hostname or ("video-leech.pro" if "video-leech" in final_link else "video-seed.pro")
            
            token_match = re.search(r'url=([^&]+)', final_link)
            if not token_match:
                print("[Driveseed-Extractor] Could not find token in Instant Link URL.")
                return None
            token = token_match.group(1)
            
            api_url = f"https://{host}/api"
            headers = {"x-token": host, "Referer": final_link}
            
            async with self.session.post(api_url, data={"keys": token}, headers=headers, ssl=False) as r:
                r.raise_for_status()
                # FIX: Read as text first to bypass content-type check
                response_text = await r.text()

            # Now, parse the text manually with regex to find the URL
            url_match = re.search(r'"url":"([^"]+)"', response_text)
            if url_match:
                url = url_match.group(1)
                if url.startswith("http"):
                    final_url = url.replace("\\/", "/")
                    print(f"[Driveseed-Extractor] SUCCESS: Extracted final stream URL: {final_url}")
                    return final_url
            else:
                print(f"[Driveseed-Extractor] Could not find 'url' pattern in API response text.")

        except Exception as e:
            print(f"[Driveseed-Extractor] InstantLink error: {e}")
        return None

    async def _extract_from_driveseed(self, url: str, callback: ExtractorCallback):
        print(f"[Driveseed-Extractor] Called for URL: {url}")
        doc = None
        
        if "r?key=" in url:
            try:
                async with self.session.get(url, timeout=15) as response:
                    page_text = await response.text()
                
                path_match = re.search(r"replace\(\"([^\"]+)\"\)", page_text)
                if not path_match:
                    print("[Driveseed-Extractor] Could not find 'replace' path in script tag.")
                    return

                path = path_match.group(1)
                real_driveseed_url = urljoin(self.driveseed_url, path)
                print(f"[Driveseed-Extractor] Resolved to real page: {real_driveseed_url}")
                doc = await self._get_soup(real_driveseed_url)
            except Exception as e:
                 print(f"[Driveseed-Extractor] Failed to resolve r?key= link: {e}")
                 return
        else:
             doc = await self._get_soup(url)
        
        if not doc or not doc.body.get_text(strip=True):
            print("[Driveseed-Extractor] Failed to get valid document from URL.")
            return

        quality_text = doc.select_one("li.list-group-item").get_text(strip=True) if doc.select_one("li.list-group-item") else ""
        quality_match = re.search(r'(\d{3,4})[pP]?', quality_text)
        quality = int(quality_match.group(1)) if quality_match else 720

        for element in doc.select("div.text-center > a"):
            text = element.get_text(strip=True)
            href = element.get("href")
            if not href: continue

            final_stream_url = None
            stream_name = f"Driveseed ({quality}p)"

            if "Instant Download" in text:
                final_stream_url = await self._driveseed_instant_link(href)
            
            if final_stream_url:
                await callback(utils.ExtractorLink(
                    name=stream_name,
                    url=final_stream_url,
                    referer=url,
                    quality=quality,
                    is_m3u8=final_stream_url.endswith('.m3u8')
                ))

    # --- Main Provider Logic ---
    async def get_streams(self, details: Dict, season: Optional[int], episode: Optional[int], 
                            subtitle_callback: SubtitleCallback, callback: ExtractorCallback):
        print("[PROVIDER] Trying DramaDrip...")
        title = details.get("title") or details.get("name")
        year = (details.get("release_date") or details.get("first_air_date") or "")[:4]

        if not title: return

        search_query = f"{title} {year}" if year else title
        detail_page_url = await self._search(search_query)
        if not detail_page_url:
            detail_page_url = await self._search(title)
            if not detail_page_url: return
            
        document = await self._get_soup(detail_page_url)
        is_tv = season is not None and episode is not None
        if not is_tv: return

        print(f"[DramaDrip-Debug] Searching for Season {season}, Episode {episode}...")
        
        download_spoiler = document.select_one(".su-spoiler-title:-soup-contains('Download Links')")
        if not download_spoiler: return
            
        content_block = download_spoiler.find_next_sibling("div", class_="su-spoiler-content")
        if not content_block: return

        season_header = None
        for header in content_block.select("h2.wp-block-heading"):
            match = re.search(r'Season\s*0*(\d+)', header.get_text(strip=True), re.IGNORECASE)
            if match and int(match.group(1)) == season:
                season_header = header
                break
        
        if not season_header: return

        links_container = season_header.find_next_sibling("div")
        if not links_container: return
            
        quality_page_links = [a['href'] for a in links_container.select("div.wp-block-button a") if a.get('href')]
        
        episode_links_to_extract = []
        for link in quality_page_links:
            try:
                episode_doc = await self._get_soup(link)
                for btn in episode_doc.select("a:-soup-contains('Episode')"):
                    if re.search(rf'Episode\s*0*{episode}\b', btn.get_text(strip=True), re.IGNORECASE):
                        if ep_href := btn.get('href'):
                            episode_links_to_extract.append(ep_href)
            except Exception: continue

        print(f"[DramaDrip-Debug] Processing {len(set(episode_links_to_extract))} unique link(s).")
        tasks = [self._extract_from_driveseed(link, callback) for link in set(episode_links_to_extract) if "driveseed.org" in link]
        await asyncio.gather(*tasks)