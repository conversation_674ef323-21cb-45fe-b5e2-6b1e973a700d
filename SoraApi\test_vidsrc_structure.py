#!/usr/bin/env python3

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import aiohttp
from bs4 import BeautifulSoup

async def test_vidsrc_structure():
    async with aiohttp.ClientSession() as session:
        # Test the current VidSrc structure with different content
        test_urls = [
            "https://v2.vidsrc.me/embed/movie/550",  # Fight Club (movie)
            "https://v2.vidsrc.me/embed/tv/1399/1-1",  # Game of Thrones S1E1 (TV show)
            "https://v2.vidsrc.me/embed/movie/299536",  # Avengers: Infinity War (recent movie)
        ]

        for test_url in test_urls:

            print(f"\n{'='*60}")
            print(f"Testing VidSrc structure with URL: {test_url}")
            print(f"{'='*60}")

            try:
                async with session.get(test_url, ssl=False, timeout=10) as response:
                    print(f"Response status: {response.status}")
                    html_content = await response.text()

                    print(f"HTML content length: {len(html_content)}")
                    print(f"HTML content preview (first 500 chars):")
                    print(html_content[:500])
                    print("\n" + "="*50 + "\n")

                    # Parse with BeautifulSoup
                    soup = BeautifulSoup(html_content, 'html.parser')

                    # Look for iframes
                    iframes = soup.find_all('iframe')
                    print(f"Found {len(iframes)} iframe(s):")
                    for i, iframe in enumerate(iframes):
                        print(f"Iframe {i+1}: {iframe}")
                        if iframe.get('src'):
                            print(f"  src: {iframe.get('src')}")
                        if iframe.get('data-src'):
                            print(f"  data-src: {iframe.get('data-src')}")

                    print("\n" + "="*50 + "\n")

                    # Test if the iframe URL is accessible
                    iframe_url = None
                    if iframes and iframes[0].get('src'):
                        iframe_url = iframes[0].get('src')
                        if iframe_url.startswith('//'):
                            iframe_url = 'https:' + iframe_url

                        print(f"Testing iframe URL accessibility: {iframe_url}")
                        try:
                            async with session.get(iframe_url, ssl=False, timeout=10) as iframe_response:
                                print(f"Iframe response status: {iframe_response.status}")
                                if iframe_response.status != 200:
                                    print(f"Iframe URL is not accessible!")
                                else:
                                    iframe_content = await iframe_response.text()
                                    print(f"Iframe content length: {len(iframe_content)}")
                                    print(f"Iframe content preview: {iframe_content[:300]}...")
                        except Exception as iframe_error:
                            print(f"Error accessing iframe URL: {iframe_error}")

            except Exception as e:
                print(f"Error testing VidSrc structure: {e}")

            print(f"\n{'='*60}\n")

if __name__ == "__main__":
    asyncio.run(test_vidsrc_structure())
