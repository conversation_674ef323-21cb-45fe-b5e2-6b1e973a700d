import asyncio
import re
import base64
from typing import Dict, Optional, List
from urllib.parse import urljoin, urlparse
import aiohttp
from bs4 import BeautifulSoup

from ... import utils
from ..base_provider import BaseProvider, SubtitleCallback, ExtractorCallback


class VidSrcProvider(BaseProvider):
    """
    VidSrc streaming provider with updated extraction logic.
    Based on the new vidsrc.net structure.
    """

    def __init__(self, session: aiohttp.ClientSession):
        super().__init__(session)
        self.name = "VidSrc"
        self.main_url = "https://vidsrc.net"
        self.base_domain = "https://whisperingauroras.com"
        self.opensubtitles_api_key = "uAs2wKWO43KbIpXMDQV40I0q5FeTPr9Y"

    def _decrypt(self, param: str, decrypt_type: str) -> Optional[str]:
        """
        Decrypt function based on the new VidSrc decryption methods.
        """
        try:
            if decrypt_type == "LXVUMCoAHJ":
                return self._LXVUMCoAHJ(param)
            elif decrypt_type == "GuxKGDsA2T":
                return self._GuxKGDsA2T(param)
            elif decrypt_type == "laM1dAi3vO":
                return self._laM1dAi3vO(param)
            elif decrypt_type == "nZlUnj2VSo":
                return self._nZlUnj2VSo(param)
            elif decrypt_type == "Iry9MQXnLs":
                return self._Iry9MQXnLs(param)
            elif decrypt_type == "IGLImMhWrI":
                return self._IGLImMhWrI(param)
            elif decrypt_type == "GTAxQyTyBx":
                return self._GTAxQyTyBx(param)
            elif decrypt_type == "C66jPHx8qu":
                return self._C66jPHx8qu(param)
            elif decrypt_type == "MyL1IRSfHe":
                return self._MyL1IRSfHe(param)
            elif decrypt_type == "detdj7JHiK":
                return self._detdj7JHiK(param)
            elif decrypt_type == "bMGyx71TzQLfdonN":
                return self._bMGyx71TzQLfdonN(param)
            else:
                print(f"[{self.name}] Unknown decrypt type: {decrypt_type}")
                return None
        except Exception as e:
            print(f"[{self.name}] Decryption error for type {decrypt_type}: {e}")
            return None

    def _LXVUMCoAHJ(self, param: str) -> str:
        reversed_param = param[::-1]
        base64_param = reversed_param.replace('-', '+').replace('_', '/')
        decoded = base64.b64decode(base64_param).decode('utf-8')
        result = ""
        shift = 3
        for char in decoded:
            result += chr(ord(char) - shift)
        return result

    def _GuxKGDsA2T(self, param: str) -> str:
        reversed_param = param[::-1]
        base64_param = reversed_param.replace('-', '+').replace('_', '/')
        decoded = base64.b64decode(base64_param).decode('utf-8')
        result = ""
        shift = 7
        for char in decoded:
            result += chr(ord(char) - shift)
        return result

    def _laM1dAi3vO(self, param: str) -> str:
        reversed_param = param[::-1]
        base64_param = reversed_param.replace('-', '+').replace('_', '/')
        decoded = base64.b64decode(base64_param).decode('utf-8')
        result = ""
        shift = 5
        for char in decoded:
            result += chr(ord(char) - shift)
        return result

    def _nZlUnj2VSo(self, param: str) -> str:
        mapping = {
            'x': 'a', 'y': 'b', 'z': 'c', 'a': 'd', 'b': 'e', 'c': 'f', 'd': 'g', 'e': 'h',
            'f': 'i', 'g': 'j', 'h': 'k', 'i': 'l', 'j': 'm', 'k': 'n', 'l': 'o', 'm': 'p',
            'n': 'q', 'o': 'r', 'p': 's', 'q': 't', 'r': 'u', 's': 'v', 't': 'w', 'u': 'x',
            'v': 'y', 'w': 'z', 'X': 'A', 'Y': 'B', 'Z': 'C', 'A': 'D', 'B': 'E', 'C': 'F',
            'D': 'G', 'E': 'H', 'F': 'I', 'G': 'J', 'H': 'K', 'I': 'L', 'J': 'M', 'K': 'N',
            'L': 'O', 'M': 'P', 'N': 'Q', 'O': 'R', 'P': 'S', 'Q': 'T', 'R': 'U', 'S': 'V',
            'T': 'W', 'U': 'X', 'V': 'Y', 'W': 'Z'
        }
        result = ""
        for char in param:
            result += mapping.get(char, char)
        return result

    def _Iry9MQXnLs(self, param: str) -> str:
        key = "pWB9V)[*4I`nJpp?ozyB~dbr9yt!_n4u"
        hex_decoded = bytes.fromhex(param).decode('utf-8')
        xor_result = ""
        for i, char in enumerate(hex_decoded):
            xor_result += chr(ord(char) ^ ord(key[i % len(key)]))

        shift_result = ""
        for char in xor_result:
            shift_result += chr(ord(char) - 3)

        return base64.b64decode(shift_result).decode('utf-8')

    def _IGLImMhWrI(self, param: str) -> str:
        reversed_param = param[::-1]
        rot13_result = ""
        for char in reversed_param:
            if char.isalpha():
                if char.lower() < 'n':
                    rot13_result += chr(ord(char) + 13)
                else:
                    rot13_result += chr(ord(char) - 13)
            else:
                rot13_result += char
        final_reversed = rot13_result[::-1]
        return base64.b64decode(final_reversed).decode('utf-8')

    def _GTAxQyTyBx(self, param: str) -> str:
        reversed_param = param[::-1]
        result = ""
        for i in range(0, len(reversed_param), 2):
            result += reversed_param[i]
        return base64.b64decode(result).decode('utf-8')

    def _C66jPHx8qu(self, param: str) -> str:
        reversed_param = param[::-1]
        key = "X9a(O;FMV2-7VO5x;Ao:dN1NoFs?j,"
        hex_decoded = bytes.fromhex(reversed_param).decode('utf-8')
        result = ""
        for i, char in enumerate(hex_decoded):
            result += chr(ord(char) ^ ord(key[i % len(key)]))
        return result

    def _MyL1IRSfHe(self, param: str) -> str:
        reversed_param = param[::-1]
        shift_result = ""
        for char in reversed_param:
            shift_result += chr(ord(char) - 1)

        hex_result = ""
        for i in range(0, len(shift_result), 2):
            hex_result += chr(int(shift_result[i:i+2], 16))
        return hex_result

    def _detdj7JHiK(self, param: str) -> str:
        trimmed = param[10:-16]
        key = "3SAY~#%Y(V%>5d/Yg\"$G[Lh1rK4a;7ok"
        decoded = base64.b64decode(trimmed).decode('utf-8')
        extended_key = (key * ((len(decoded) // len(key)) + 1))[:len(decoded)]

        result = ""
        for i, char in enumerate(decoded):
            result += chr(ord(char) ^ ord(extended_key[i]))
        return result

    def _bMGyx71TzQLfdonN(self, param: str) -> str:
        chunk_size = 3
        chunks = []
        for i in range(0, len(param), chunk_size):
            chunks.append(param[i:i + chunk_size])
        return ''.join(reversed(chunks))

    async def _extract_servers(self, soup, embed_url):
        """
        Extract servers from the embed page and update base domain.
        """
        servers = []
        title = soup.find('title')
        title_text = title.get_text() if title else ""

        # Update base domain from iframe src
        iframe = soup.find('iframe')
        if iframe and iframe.get('src'):
            iframe_src = iframe.get('src')
            if iframe_src.startswith('//'):
                iframe_src = 'https:' + iframe_src
            elif iframe_src.startswith('/'):
                iframe_src = self.main_url + iframe_src

            # Extract domain from iframe URL
            from urllib.parse import urlparse
            parsed = urlparse(iframe_src)
            if parsed.netloc:
                self.base_domain = f"{parsed.scheme}://{parsed.netloc}"
                print(f"[{self.name}] Updated base domain to: {self.base_domain}")

        # Extract servers
        server_elements = soup.select('.serversList .server')
        for element in server_elements:
            server_name = element.get_text(strip=True)
            data_hash = element.get('data-hash')

            servers.append({
                'name': server_name,
                'data_hash': data_hash
            })

        return servers, title_text

    async def _extract_rcp_source(self, rcp_text):
        """
        Extract source URL from RCP response.
        """
        import re

        # Debug: Print the RCP response content
        print(f"[{self.name}] RCP response content preview: {rcp_text[:500]}...")

        # Try multiple patterns for finding the source
        patterns = [
            r"src:\s*['\"]([^'\"]*)['\"]",
            r"source:\s*['\"]([^'\"]*)['\"]",
            r"file:\s*['\"]([^'\"]*)['\"]",
            r"url:\s*['\"]([^'\"]*)['\"]",
            r"player:\s*['\"]([^'\"]*)['\"]",
            r"stream:\s*['\"]([^'\"]*)['\"]"
        ]

        for pattern in patterns:
            match = re.search(pattern, rcp_text)
            if match:
                print(f"[{self.name}] Found source with pattern '{pattern}': {match.group(1)}")
                return match.group(1)

        # If no patterns match, look for any URLs that might be sources
        url_patterns = [
            r'["\']([^"\']*\.m3u8[^"\']*)["\']',
            r'["\']([^"\']*\.mp4[^"\']*)["\']',
            r'["\']([^"\']*prorcp[^"\']*)["\']',
            r'["\']([^"\']*embed[^"\']*)["\']'
        ]

        for pattern in url_patterns:
            matches = re.findall(pattern, rcp_text)
            if matches:
                print(f"[{self.name}] Found potential URLs with pattern '{pattern}': {matches}")
                return matches[0]

        print(f"[{self.name}] No source patterns found in RCP response")
        return None

    async def _handle_prorcp(self, prorcp_id):
        """
        Handle PRORCP extraction to get the final stream URL.
        """
        try:
            prorcp_url = f"{self.base_domain}/prorcp/{prorcp_id}"
            print(f"[{self.name}] Fetching PRORCP: {prorcp_url}")

            async with self.session.get(prorcp_url, ssl=False) as response:
                if response.status != 200:
                    print(f"[{self.name}] PRORCP request failed with status: {response.status}")
                    return None

                prorcp_text = await response.text()
                print(f"[{self.name}] PRORCP response length: {len(prorcp_text)}")

                # Extract script sources
                import re
                script_pattern = r'<script\s+src="\/([^"]*\.js)\?\_=([^"]*)"><\/script>'
                scripts = re.findall(script_pattern, prorcp_text)

                if not scripts:
                    print(f"[{self.name}] No scripts found in PRORCP response")
                    return None

                # Get the last script that's not cpt.js
                target_script = None
                for script_path, script_param in scripts:
                    if 'cpt.js' not in script_path:
                        target_script = f"{script_path}?_={script_param}"

                if not target_script:
                    print(f"[{self.name}] No suitable script found")
                    return None

                print(f"[{self.name}] Target script: {target_script}")

                # Fetch the JavaScript file
                js_url = f"{self.base_domain}/{target_script}"
                async with self.session.get(js_url, ssl=False, headers={'Referer': f"{self.base_domain}/"}) as js_response:
                    if js_response.status != 200:
                        print(f"[{self.name}] JS request failed with status: {js_response.status}")
                        return None

                    js_code = await js_response.text()
                    print(f"[{self.name}] JS code length: {len(js_code)}")

                    # Extract decrypt function info
                    decrypt_pattern = r'\{\}\}window\[([^"]+)\("([^"]+)"\)'
                    decrypt_match = re.search(decrypt_pattern, js_code)

                    if not decrypt_match:
                        print(f"[{self.name}] No decrypt pattern found in JS")
                        return None

                    decrypt_func = decrypt_match.group(1)
                    decrypt_key = decrypt_match.group(2)
                    print(f"[{self.name}] Decrypt function: {decrypt_func}, key: {decrypt_key}")

                    # Parse PRORCP HTML to find encrypted data
                    from bs4 import BeautifulSoup
                    soup = BeautifulSoup(prorcp_text, 'html.parser')

                    # Decrypt the element ID
                    element_id = self._decrypt(decrypt_key, decrypt_func)
                    if not element_id:
                        print(f"[{self.name}] Failed to decrypt element ID")
                        return None

                    print(f"[{self.name}] Decrypted element ID: {element_id}")

                    # Find the element with the decrypted ID
                    target_element = soup.find(id=element_id)
                    if not target_element:
                        print(f"[{self.name}] Element with ID '{element_id}' not found")
                        return None

                    encrypted_data = target_element.get_text()
                    print(f"[{self.name}] Encrypted data: {encrypted_data[:100]}...")

                    # Decrypt the final data
                    stream_url = self._decrypt(encrypted_data, decrypt_key)
                    if stream_url:
                        print(f"[{self.name}] Successfully decrypted stream URL")
                        return stream_url
                    else:
                        print(f"[{self.name}] Failed to decrypt stream URL")
                        return None

        except Exception as e:
            print(f"[{self.name}] Error in PRORCP handling: {e}")
            return None
    
    async def _fetch_opensubtitles(self, details: Dict, subtitle_callback: SubtitleCallback, season: Optional[int] = None, episode: Optional[int] = None):
        """
        Fetch subtitles from OpenSubtitles API.
        """
        try:
            raw_imdb_id = details.get("imdb_id") or details.get("external_ids", {}).get("imdb_id")
            if not raw_imdb_id:
                return
            
            imdb_id = raw_imdb_id.replace("tt", "")
            params = {"imdb_id": imdb_id, "languages": "en,ar,ko"}
            if season: 
                params["season_number"] = season
            if episode: 
                params["episode_number"] = episode
            
            headers = {
                'Api-Key': self.opensubtitles_api_key, 
                'User-Agent': 'SoraStream v1'
            }
            
            async with self.session.get(
                "https://api.opensubtitles.com/api/v1/subtitles", 
                params=params, 
                headers=headers, 
                timeout=4
            ) as response:
                if response.status != 200: 
                    return
                
                data = await response.json()
                lang_subs = {}
                
                for sub in data.get('data', []):
                    attrs = sub.get('attributes', {})
                    lang_code = attrs.get('language')
                    if not lang_code: 
                        continue
                    
                    file_id = next((f.get('file_id') for f in attrs.get('files', []) if f.get('file_id')), None)
                    if not file_id: 
                        continue
                    
                    score = (not attrs.get('hearing_impaired', False), attrs.get('download_count', 0))
                    if lang_code not in lang_subs or score > lang_subs[lang_code]['score']:
                        lang_subs[lang_code] = {
                            'score': score,
                            'url': f"/subtitles/new_{file_id}.vtt",
                            'lang_name': attrs.get('language_name', lang_code.capitalize())
                        }
                
                for lang, sub_info in lang_subs.items():
                    await subtitle_callback(utils.SubtitleFile(
                        lang=sub_info['lang_name'], 
                        url=sub_info['url']
                    ))
                    
        except Exception as e:
            print(f"[{self.name}] Subtitles API request failed: {e}")
    
    async def get_streams(self, details: Dict, season: Optional[int], episode: Optional[int],
                         subtitle_callback: SubtitleCallback, callback: ExtractorCallback) -> None:
        """
        Extract streams from VidSrc using the new vidsrc.net structure.
        """
        print(f"[{self.name}] Trying VidSrc with new extraction method...")
        tmdb_id = details.get("id")
        print(f"[{self.name}] TMDB ID: {tmdb_id}")

        # Start subtitle fetching task
        subtitle_task = asyncio.create_task(
            self._fetch_opensubtitles(details, subtitle_callback, season, episode)
        )

        try:
            # Build the embed URL using the new structure
            media_type = "tv" if season is not None else "movie"
            if media_type == "movie":
                embed_url = f"{self.main_url}/embed/{media_type}?tmdb={tmdb_id}"
            else:
                embed_url = f"{self.main_url}/embed/{media_type}?tmdb={tmdb_id}&season={season}&episode={episode}"

            print(f"[{self.name}] Step 1: Fetching embed page: {embed_url}")

            # Get the embed page
            embed_soup = await self._get_soup(embed_url)
            print(f"[{self.name}] Step 1: Got embed page, extracting servers...")

            # Extract servers and update base domain
            servers, title = await self._extract_servers(embed_soup, embed_url)
            print(f"[{self.name}] Step 1: Found {len(servers)} servers for '{title}'")

            if not servers:
                print(f"[{self.name}] No servers found")
                await subtitle_task
                return

            # Process each server
            stream_count = 0
            for i, server in enumerate(servers):
                print(f"[{self.name}] Step 2: Processing server {i+1}: {server['name']} (hash: {server['data_hash']})")

                if not server['data_hash']:
                    print(f"[{self.name}] Server {i+1}: No data hash, skipping")
                    continue

                # Get RCP data
                rcp_url = f"{self.base_domain}/rcp/{server['data_hash']}"
                print(f"[{self.name}] Step 2: Fetching RCP: {rcp_url}")

                try:
                    async with self.session.get(rcp_url, ssl=False) as rcp_response:
                        if rcp_response.status != 200:
                            print(f"[{self.name}] RCP request failed with status: {rcp_response.status}")
                            continue

                        rcp_text = await rcp_response.text()
                        print(f"[{self.name}] Step 2: RCP response length: {len(rcp_text)}")

                        # Extract source from RCP response
                        rcp_data = await self._extract_rcp_source(rcp_text)
                        if not rcp_data:
                            print(f"[{self.name}] No source found in RCP response")
                            continue

                        print(f"[{self.name}] Step 3: RCP data: {rcp_data}")

                        # Handle different RCP types
                        if rcp_data.startswith("/prorcp/"):
                            prorcp_id = rcp_data.replace("/prorcp/", "")
                            print(f"[{self.name}] Step 3: Processing PRORCP: {prorcp_id}")

                            stream_url = await self._handle_prorcp(prorcp_id)
                            if stream_url:
                                print(f"[{self.name}] Step 4: Found stream URL: {stream_url[:100]}...")

                                await callback(utils.ExtractorLink(
                                    name=f"VidSrc ({server['name']})",
                                    url=stream_url,
                                    referer=self.base_domain,
                                    quality=720,  # Default quality, could be improved
                                    is_m3u8=True
                                ))
                                stream_count += 1
                            else:
                                print(f"[{self.name}] Failed to extract stream from PRORCP")
                        else:
                            print(f"[{self.name}] Unknown RCP type: {rcp_data}")

                except Exception as server_error:
                    print(f"[{self.name}] Error processing server {i+1}: {server_error}")
                    continue

            print(f"[{self.name}] Step 5: Total streams found: {stream_count}")

        except Exception as e:
            print(f"[{self.name}] An error occurred during extraction: {e}")
            import traceback
            print(f"[{self.name}] Traceback: {traceback.format_exc()}")
        finally:
            await subtitle_task
