#!/usr/bin/env python3

import asyncio
import aiohttp

async def test_rcp_content():
    async with aiohttp.ClientSession() as session:
        # Test a specific RCP URL from the logs
        rcp_url = "https://cloudnestra.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"
        
        print(f"Testing RCP URL: {rcp_url}")
        
        try:
            async with session.get(rcp_url, ssl=False, timeout=10) as response:
                print(f"Response status: {response.status}")
                print(f"Response headers: {dict(response.headers)}")
                
                content = await response.text()
                print(f"Content length: {len(content)}")
                print(f"Full content:")
                print("="*80)
                print(content)
                print("="*80)
                
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_rcp_content())
