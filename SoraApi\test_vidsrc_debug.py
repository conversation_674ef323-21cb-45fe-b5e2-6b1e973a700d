#!/usr/bin/env python3

import asyncio
import sys
import os
from urllib.parse import urlparse

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import aiohttp
from api.providers.vidsrc.vidsrc import VidSrcProvider

BASE_HEADERS = {
    "User-Agent": (
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
        "AppleWebKit/537.36 (KHTML, like Gecko) "
        "Chrome/********* Safari/537.36"
    ),
    "Accept-Language": "en-US,en;q=0.9",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
}

VSR_HEADERS = {
    "Referer": "https://v2.vidsrc.me/",
    "Origin": "https://v2.vidsrc.me",
}

def request_with_vidsrc_headers(session, url, **kwargs):
    headers = BASE_HEADERS.copy()
    domain = urlparse(url).hostname or ""
    if "vidsrc.me" in domain:
        headers.update(VSR_HEADERS)
    return session.get(url, headers=headers, **kwargs)  # return context manager

async def test_vidsrc():
    async with aiohttp.ClientSession() as session:
        print("Testing VidSrc accessibility...")
        try:
            async with session.get("https://vidsrc.net", ssl=False, timeout=10) as response:
                print(f"VidSrc main site status: {response.status}")
                if response.status != 200:
                    print("VidSrc main site is not accessible!")
                    return
        except Exception as e:
            print(f"Failed to access VidSrc: {e}")
            return

        provider = VidSrcProvider(session)

        test_movies = [
            {'id': 299536, 'title': 'Avengers: Infinity War'},
            {'id': 550, 'title': 'Fight Club'},
            {'id': 157336, 'title': 'Interstellar'},
        ]

        for movie in test_movies:
            print(f"\n=== Testing {movie['title']} (ID: {movie['id']}) ===")
            details = {'id': movie['id']}

            streams = []
            subtitles = []

            async def stream_callback(stream):
                streams.append(stream)
                print(f"Stream found: {stream.name} - Quality: {stream.quality}p")
                print(f"URL: {stream.url}")  # print full URL

            async def subtitle_callback(subtitle):
                subtitles.append(subtitle)
                print(f"Subtitle found: {subtitle.lang} - {subtitle.url}")

            await provider.get_streams(details, None, None, subtitle_callback, stream_callback)

            print(f'Found {len(streams)} streams and {len(subtitles)} subtitles')

            if streams:
                for i, stream in enumerate(streams):
                    print(f'Stream {i+1}: {stream.name} - {stream.url}')
                break
            else:
                print("No streams found for this movie, trying next...")

        print(f'\n=== FINAL SUMMARY ===')
        print(f'Total streams found: {len(streams)}')

if __name__ == "__main__":
    asyncio.run(test_vidsrc())
