#!/usr/bin/env python3

import asyncio
import aiohttp

async def test_cloudnestra():
    async with aiohttp.ClientSession() as session:
        # Test if cloudnestra.com is accessible
        test_urls = [
            "https://cloudnestra.com",
            "http://cloudnestra.com",
            "https://cloudnestra.com/rcp/test",
        ]
        
        for url in test_urls:
            print(f"Testing: {url}")
            try:
                async with session.get(url, ssl=False, timeout=10) as response:
                    print(f"  Status: {response.status}")
                    content = await response.text()
                    print(f"  Content length: {len(content)}")
                    print(f"  Content preview: {content[:200]}...")
            except Exception as e:
                print(f"  Error: {e}")
            print()

if __name__ == "__main__":
    asyncio.run(test_cloudnestra())
