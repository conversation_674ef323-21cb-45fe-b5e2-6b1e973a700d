import asyncio
import re
import base64
import traceback
from typing import Dict, Optional
from bs4 import BeautifulSoup
import requests

from ... import utils
from ..base_provider import BaseProvider, SubtitleCallback, ExtractorCallback


def _extract_voe_sx_link(initial_url: str, referer: str) -> str | None:
    """
    A specialized, one-to-one Python implementation of the provided Swift VoeExtractor logic.
    It handles redirects and Base64 decoding to find the true video URL.
    """
    try:
        page_response = requests.get(initial_url, headers={'Referer': referer}, timeout=5)
        page_response.raise_for_status()
        html_content = page_response.text

        redirect_match = re.search(r"window\.location\.href\s*=\s*['\"](https?://[^'\"]+)['\"]", html_content)
        if redirect_match:
            redirect_url = redirect_match.group(1)
            print(f"    -> Voe.sx redirected to: {redirect_url}")
            page_response = requests.get(redirect_url, headers={'Referer': initial_url}, timeout=5)
            page_response.raise_for_status()
            html_content = page_response.text

        match = re.search(r"'hls':\s*'([^']+)'", html_content)
        if not match:
            return None
        
        base64_string = match.group(1)
        decoded_bytes = base64.b64decode(base64_string)
        final_url = decoded_bytes.decode('utf-8')
        
        return final_url
            
    except Exception as e:
        print(f"    -> Voe extractor failed with error: {e}")
        return None


def _extract_stream_from_server(iframe_url: str, watch_url: str) -> str | None:
    """
    Extract stream URL from a server iframe URL.
    Handles different server types including Voe.sx and generic extractors.
    """
    try:
        final_link = None
        if "voe.sx" in iframe_url:
            print("    -> Detected Voe.sx link, using specialized Base64 extractor...")
            final_link = _extract_voe_sx_link(iframe_url, watch_url)
        else:
            try:
                print("    -> Using generic extractor...")
                iframe_response = requests.get(iframe_url, headers={'Referer': watch_url}, timeout=4)
                iframe_content = iframe_response.text
                if match := re.search(r'file:"(https?://[^"]+)"', iframe_content):
                    final_link = match.group(1)
                elif match := re.search(r'["\'](https?://[^\s"\']+\.(?:mp4|m3u8)[^\s"\']*)["\']', iframe_content):
                    final_link = match.group(1)
                elif source_tag := BeautifulSoup(iframe_content, 'html.parser').select_one("source[src]"):
                    final_link = source_tag['src']
            except requests.exceptions.RequestException as e:
                print(f"    Failed: Could not connect to this server. Error: {e}")
                return None

        if final_link:
            print(f"    Success: Found stream link.")
            return final_link
        else:
            print("    Failed: No stream found on this server.")
            return None

    except Exception as e:
        print(f"    Error extracting from server: {e}")
        return None


def _normalize_arabic_text(text: str) -> str:
    """
    Normalize Arabic text by removing diacritics and standardizing characters.
    This is a standalone function for use in the sync functions.
    """
    if not text:
        return ""

    # Remove Arabic diacritics (tashkeel)
    text = re.sub(r'[\u064B-\u0652\u0670]', '', text)

    # Remove tatweel (kashida)
    text = re.sub(r'ـ', '', text)

    # Normalize alif variations
    text = re.sub(r'[أإآ]', 'ا', text)

    # Normalize taa marbuta to haa
    text = re.sub(r'ة', 'ه', text)

    # Normalize alif maksura to yaa
    text = re.sub(r'ى', 'ي', text)

    return text


def _run_arabseed_sync(show_name: str, season_number_to_find: int, episode_number_to_find: int, is_movie: bool) -> Optional[tuple[str, int]]:
    """
    Main synchronous function that probes servers and uses specialized extractors.
    Returns a tuple of (link, quality) on success.
    """
    main_url = "https://a.asd.homes"
    print(f"[ArabSeed Sync] Using Main URL: {main_url}")

    try:
        # *** FIX: Replace ' & ' with ' and ' in the show name for better search compatibility. ***
        search_name = show_name.replace(' & ', ' and ')

        # Normalize Arabic text to improve search matching
        normalized_search_name = _normalize_arabic_text(search_name)
        print(f"\n[ArabSeed Sync] Searching for '{normalized_search_name}' (original: '{show_name}', cleaned: '{search_name}')...")
        
        search_url = f"{main_url}/wp-content/themes/Elshaikh2021/Ajaxat/SearchingTwo.php"
        search_payload = {'search': normalized_search_name, 'type': 'all'}
        search_response = requests.post(search_url, data=search_payload, headers={'Referer': main_url})
        search_soup = BeautifulSoup(search_response.content, 'html.parser')

        # Try to find media page URL using normalized search name first, then fallback to original
        media_page_url = None
        for result in search_soup.select("ul.Blocks-UL > div"):
            title_element = result.select_one("h4")
            if title_element:
                result_title = title_element.text
                normalized_result_title = _normalize_arabic_text(result_title)
                # Check if normalized search name is in normalized result title
                if normalized_search_name in normalized_result_title or search_name in result_title:
                    media_page_url = result.select_one("a")['href']
                    print(f"[ArabSeed Sync] Found match: '{result_title}' -> '{media_page_url}'")
                    break
        if not media_page_url:
            print(f"[ArabSeed Sync] Could not find any entry for the media in search results.")
            print(f"[ArabSeed Sync] Searched for: '{normalized_search_name}' and '{search_name}'")
            # Print available results for debugging
            available_titles = [result.select_one("h4").text for result in search_soup.select("ul.Blocks-UL > div") if result.select_one("h4")]
            if available_titles:
                print(f"[ArabSeed Sync] Available titles: {available_titles[:5]}")  # Show first 5 results
            return None
        print(f"[ArabSeed Sync] Found a page for the media: {media_page_url}")

        page_response = requests.get(media_page_url, headers={'Referer': main_url})
        page_soup = BeautifulSoup(page_response.content, 'html.parser')

        watch_button = None
        episode_link_for_referer = None

        if is_movie:
            print("[ArabSeed Sync] Movie detected. Searching for watch button directly on the page.")
            watch_button = page_soup.select_one("a.watchBTn")
            episode_link_for_referer = media_page_url
        else: 
            print("[ArabSeed Sync] TV show detected. Searching for season and episode...")
            episode_list_soup = None
            if season_holder := page_soup.select_one("div.SeasonsListHolder"):
                print(f"[ArabSeed Sync] Season list found. Selecting Season {season_number_to_find}...")
                seasons = season_holder.select("ul li")
                if len(seasons) < season_number_to_find: return None
                target_season = seasons[season_number_to_find - 1]
                ep_url = f"{main_url}/wp-content/themes/Elshaikh2021/Ajaxat/Single/Episodes.php"
                ep_payload = {'season': target_season['data-season'], 'post_id': target_season['data-id']}
                ep_response = requests.post(ep_url, data=ep_payload, headers={'Referer': media_page_url})
                episode_list_soup = BeautifulSoup(ep_response.content, 'html.parser')
            else:
                episode_list_soup = page_soup.select_one("div.ContainerEpisodesList")

            if not episode_list_soup: return None

            episode_link = next((link['href'] for link in episode_list_soup.find_all("a") if (em := link.find("em")) and em.text.strip() == str(episode_number_to_find)), None)
            if not episode_link:
                print(f"[ArabSeed Sync] Could not find Episode {episode_number_to_find} in the selected season.")
                return None
            print(f"[ArabSeed Sync] Found link for Episode {episode_number_to_find}: {episode_link}")
            
            ep_page_res = requests.get(episode_link, headers={'Referer': media_page_url})
            watch_button = BeautifulSoup(ep_page_res.content, 'html.parser').select_one("a.watchBTn")
            episode_link_for_referer = episode_link

        if not watch_button:
            print("[ArabSeed Sync] Could not find the watch button.")
            return None
        
        watch_url = watch_button['href']
        print(f"[ArabSeed Sync] Found watch page: {watch_url}")

        watch_page_response = requests.get(watch_url, headers={'Referer': episode_link_for_referer})
        watch_soup = BeautifulSoup(watch_page_response.content, 'html.parser')

        # Store all found streams with their qualities
        found_streams = []

        # Method 1: Try the standard structure with quality headers
        print("\n[ArabSeed Sync] === Trying Method 1: Quality Headers Structure ===")
        method1_success = False

        for quality in ["1080", "720", "480"]:
            print(f"\n[ArabSeed Sync] --- Checking for {quality}p quality ---")
            quality_header = watch_soup.find("h3", string=re.compile(f"مشاهدة {quality}"))
            if not quality_header:
                print(f"[ArabSeed Sync] No {quality}p quality section found")
                continue

            server_links_for_quality = []
            current_element = quality_header.find_next_sibling()
            while current_element and current_element.name != 'h3':
                if current_element.name == 'li' and 'data-link' in current_element.attrs:
                    server_links_for_quality.append(current_element['data-link'])
                current_element = current_element.find_next_sibling()

            if not server_links_for_quality:
                print(f"[ArabSeed Sync] No server links found for {quality}p")
                continue

            # Try to find one working stream for this quality
            quality_found = False
            for i, iframe_url in enumerate(server_links_for_quality, 1):
                if quality_found:  # Skip remaining servers if we already found one for this quality
                    break

                print(f"[ArabSeed Sync] Probing {quality}p server {i}/{len(server_links_for_quality)}: {iframe_url}")

                final_link = _extract_stream_from_server(iframe_url, watch_url)
                if final_link:
                    print(f"    Success: Found {quality}p stream link.")
                    found_streams.append((final_link, int(quality)))
                    quality_found = True
                    method1_success = True

        # Method 2: Fallback to containerServers structure if Method 1 failed
        if not method1_success:
            print("\n[ArabSeed Sync] === Method 1 failed, trying Method 2: containerServers Structure ===")

            # Look for the containerServers div
            container_servers = watch_soup.select_one("div.containerServers ul")
            if container_servers:
                print("[ArabSeed Sync] Found containerServers structure")

                # Group servers by quality
                quality_servers = {"1080": [], "720": [], "480": [], "unknown": []}

                for li in container_servers.select("li[data-link]"):
                    iframe_url = li.get('data-link')
                    span_text = li.select_one("span")

                    if not iframe_url or not span_text:
                        continue

                    server_text = span_text.text.strip()
                    print(f"[ArabSeed Sync] Found server: {server_text} -> {iframe_url}")

                    # Extract quality from server text
                    quality = "unknown"
                    if "1080p" in server_text or "1080" in server_text:
                        quality = "1080"
                    elif "720p" in server_text or "720" in server_text:
                        quality = "720"
                    elif "480p" in server_text or "480" in server_text:
                        quality = "480"

                    quality_servers[quality].append(iframe_url)

                # Try to get one working stream for each quality
                for quality in ["1080", "720", "480", "unknown"]:
                    if not quality_servers[quality]:
                        continue

                    print(f"\n[ArabSeed Sync] --- Trying {quality}p servers ({len(quality_servers[quality])} available) ---")

                    quality_found = False
                    for i, iframe_url in enumerate(quality_servers[quality], 1):
                        if quality_found:
                            break

                        print(f"[ArabSeed Sync] Probing {quality}p server {i}/{len(quality_servers[quality])}: {iframe_url}")

                        final_link = _extract_stream_from_server(iframe_url, watch_url)
                        if final_link:
                            quality_int = int(quality) if quality.isdigit() else 720  # Default to 720p for unknown
                            print(f"    Success: Found {quality}p stream link.")
                            found_streams.append((final_link, quality_int))
                            quality_found = True
            else:
                print("[ArabSeed Sync] No containerServers structure found either")

        if found_streams:
            print(f"\n[ArabSeed Sync] Found {len(found_streams)} quality streams: {[f'{q}p' for _, q in found_streams]}")
            return found_streams  # Return all found streams instead of just one
        else:
            print("\n[ArabSeed Sync] All methods and servers probed. Could not find any working stream links.")
            return None

    except requests.exceptions.RequestException as e:
        print(f"[ArabSeed Sync] A critical network error occurred: {e}")
        return None
    except Exception as e:
        print(f"[ArabSeed Sync] An unexpected error occurred: {e}")
        traceback.print_exc()
        return None


class ArabSeedProvider(BaseProvider):
    """
    ArabSeed streaming provider with synchronous extraction logic.
    """

    def __init__(self, session):
        super().__init__(session)
        self.name = "ArabSeed"
        self.main_url = "https://a.asd.homes"

    def _normalize_arabic(self, text: str) -> str:
        """
        Normalize Arabic text by removing diacritics and standardizing characters.

        Args:
            text: Arabic text to normalize

        Returns:
            Normalized Arabic text
        """
        if not text:
            return ""

        # Remove Arabic diacritics (tashkeel)
        text = re.sub(r'[\u064B-\u0652\u0670]', '', text)

        # Remove tatweel (kashida)
        text = re.sub(r'ـ', '', text)

        # Normalize alif variations
        text = re.sub(r'[أإآ]', 'ا', text)

        # Normalize taa marbuta to haa
        text = re.sub(r'ة', 'ه', text)

        # Normalize alif maksura to yaa
        text = re.sub(r'ى', 'ي', text)

        return text
    
    async def get_streams(self, details: Dict, season: Optional[int], episode: Optional[int],
                         subtitle_callback: SubtitleCallback, callback: ExtractorCallback) -> None:
        """
        Extract streams from ArabSeed using synchronous helper functions.
        """
        print(f"[{self.name}] Trying ArabSeed...")
        is_movie = season is None
        show_name = details.get("original_title") if is_movie else details.get("original_name")
        season_number = 1 if is_movie else season
        episode_number = 1 if is_movie else episode

        if not show_name:
            print(f"[{self.name}] Could not find original title/name in TMDB details.")
            return

        try:
            loop = asyncio.get_running_loop()
            result = await loop.run_in_executor(
                None,
                _run_arabseed_sync,
                show_name,
                season_number,
                episode_number,
                is_movie
            )

            if result:
                # Handle multiple streams (list of tuples) or single stream (single tuple)
                if isinstance(result, list):
                    # Multiple streams found
                    print(f"[{self.name}] Successfully extracted {len(result)} streams via sync runner")
                    for final_link, quality in result:
                        await callback(utils.ExtractorLink(
                            name=f"ArabSeed ({quality}p)",
                            url=final_link,
                            referer=self.main_url + "/",
                            quality=quality,
                            is_m3u8=".m3u8" in final_link
                        ))
                        print(f"[{self.name}] Added {quality}p stream: {final_link}")
                else:
                    # Single stream found (backward compatibility)
                    final_link, quality = result
                    print(f"[{self.name}] Successfully extracted single stream via sync runner: {final_link}")
                    await callback(utils.ExtractorLink(
                        name=f"ArabSeed ({quality}p)",
                        url=final_link,
                        referer=self.main_url + "/",
                        quality=quality,
                        is_m3u8=".m3u8" in final_link
                    ))
            else:
                print(f"[{self.name}] Sync runner did not find any working links.")

        except Exception as e:
            print(f"[{self.name}] An unexpected error occurred in the async wrapper: {e}")
            traceback.print_exc()
